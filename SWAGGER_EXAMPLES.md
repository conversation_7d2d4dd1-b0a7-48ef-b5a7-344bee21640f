# 🧪 Exemplos de Teste no Swagger

Este arquivo contém exemplos práticos de como testar cada endpoint usando o Swagger UI.

## 🌐 Acesso ao Swagger

**URL**: http://localhost:3000/api

## 📋 Exemplos de Teste

### 1. 🔍 Verificar Status da Conexão

**Endpoint**: `GET /whatsapp/status`

**Passos**:
1. <PERSON>bra o Swagger UI
2. Localize "GET /whatsapp/status"
3. Clique para expandir
4. Clique em "Try it out"
5. Clique em "Execute"

**Resposta Esperada**:
```json
{
  "connected": false,
  "qrCode": "2@...",
  "state": "close"
}
```

### 2. 📱 Enviar Mensagem

**Endpoint**: `POST /whatsapp/send-message`

**Passos**:
1. Localize "POST /whatsapp/send-message"
2. Clique para expandir
3. Clique em "Try it out"
4. Edite o JSON no campo "Request body":

```json
{
  "to": "5511999999999",
  "message": "Olá! Esta é uma mensagem de teste enviada via Swagger.",
  "type": "text"
}
```

5. Clique em "Execute"

**Resposta de Sucesso**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "messageId": "3EB0C431C26A1916E9E5",
    "to": "<EMAIL>",
    "message": "Olá! Esta é uma mensagem de teste enviada via Swagger."
  }
}
```

**Resposta de Erro**:
```json
{
  "success": false,
  "message": "WhatsApp is not connected"
}
```

### 3. 📜 Buscar Histórico de Conversa

**Endpoint**: `GET /whatsapp/chat/{chatId}/history`

**Passos**:
1. Localize "GET /whatsapp/chat/{chatId}/history"
2. Clique para expandir
3. Clique em "Try it out"
4. Preencha os parâmetros:
   - **chatId**: `5511999999999`
   - **limit**: `20` (opcional)
5. Clique em "Execute"

**Resposta Esperada**:
```json
{
  "success": true,
  "data": {
    "id": "<EMAIL>",
    "name": "5511999999999",
    "messages": [
      {
        "id": "3EB0C431C26A1916E9E5",
        "from": "<EMAIL>",
        "to": "<EMAIL>",
        "message": "Olá! Como você está?",
        "timestamp": 1672531200,
        "fromMe": false,
        "messageType": "conversation"
      }
    ]
  }
}
```

### 4. 💬 Listar Todas as Conversas

**Endpoint**: `GET /whatsapp/chats`

**Passos**:
1. Localize "GET /whatsapp/chats"
2. Clique para expandir
3. Clique em "Try it out"
4. Clique em "Execute"

**Resposta Esperada**:
```json
{
  "success": true,
  "data": [
    {
      "id": "<EMAIL>",
      "name": "5511999999999",
      "messages": [
        {
          "id": "3EB0C431C26A1916E9E5",
          "from": "<EMAIL>",
          "to": "<EMAIL>",
          "message": "Última mensagem",
          "timestamp": 1672531200,
          "fromMe": true,
          "messageType": "conversation"
        }
      ]
    }
  ]
}
```

### 5. 🔌 Desconectar do WhatsApp

**Endpoint**: `POST /whatsapp/disconnect`

**Passos**:
1. Localize "POST /whatsapp/disconnect"
2. Clique para expandir
3. Clique em "Try it out"
4. Clique em "Execute"

**Resposta Esperada**:
```json
{
  "success": true,
  "message": "WhatsApp disconnected successfully"
}
```

## 🎯 Cenários de Teste

### Cenário 1: Primeiro Uso
1. **Status** → Verificar se não está conectado
2. **Conectar** → Escanear QR Code no terminal
3. **Status** → Verificar se está conectado
4. **Enviar** → Enviar mensagem de teste
5. **Histórico** → Verificar se a mensagem foi salva

### Cenário 2: Uso Normal
1. **Status** → Confirmar conexão
2. **Listar** → Ver todas as conversas
3. **Enviar** → Enviar nova mensagem
4. **Histórico** → Ver mensagens da conversa

### Cenário 3: Desconexão
1. **Status** → Verificar conexão atual
2. **Desconectar** → Desconectar do WhatsApp
3. **Status** → Confirmar desconexão
4. **Enviar** → Tentar enviar (deve falhar)

## 🔧 Dicas de Uso

### Validação de Dados
- O Swagger valida automaticamente os tipos de dados
- Campos obrigatórios são marcados com *
- Exemplos são fornecidos para cada campo

### Códigos de Status
- **200**: Sucesso
- **400**: Erro de validação ou lógica
- **500**: Erro interno do servidor

### Formatos de Número
- Use sempre formato internacional
- Exemplo: `5511999999999` (Brasil)
- Não use símbolos: `+`, `(`, `)`, `-`, espaços

### Monitoramento
- Acompanhe os logs no terminal do servidor
- Verifique o status antes de enviar mensagens
- Use o QR Code quando necessário

## 🚨 Troubleshooting

### Erro: "WhatsApp is not connected"
**Solução**: 
1. Verifique o terminal do servidor
2. Escaneie o QR Code se disponível
3. Use `GET /whatsapp/status` para confirmar

### Erro: "Failed to send message"
**Solução**:
1. Confirme que o número está correto
2. Verifique se o WhatsApp está conectado
3. Teste com um número conhecido

### Swagger não carrega
**Solução**:
1. Confirme que o servidor está rodando
2. Acesse: http://localhost:3000/api
3. Recarregue a página

## 📊 Monitoramento de Testes

### Logs Importantes
```
[WhatsappService] QR Code generated, scan it to connect
[WhatsappService] WhatsApp connection opened successfully
[WhatsappService] Message <NAME_EMAIL>
```

### Status de Conexão
- `"state": "close"` → Desconectado
- `"state": "connecting"` → Conectando
- `"state": "open"` → Conectado

### Indicadores de Sucesso
- `"connected": true` → WhatsApp conectado
- `"success": true` → Operação bem-sucedida
- `"messageId"` presente → Mensagem enviada
