export class SendMessageDto {
  to: string;
  message: string;
  type?: 'text' | 'image' | 'document' = 'text';
}

export class ChatHistoryDto {
  chatId: string;
  limit?: number = 50;
}

export class MessageResponseDto {
  success: boolean;
  messageId?: string;
  to?: string;
  message?: string;
  error?: string;
}

export class ConnectionStatusDto {
  connected: boolean;
  qrCode?: string;
  state: string;
}
