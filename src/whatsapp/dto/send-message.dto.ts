import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SendMessageDto {
  @ApiProperty({
    description:
      'Número de telefone no formato internacional (ex: 5511999999999)',
    example: '5511999999999',
  })
  to: string;

  @ApiProperty({
    description: 'Mensagem a ser enviada',
    example: 'Olá! Esta é uma mensagem de teste.',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Tipo da mensagem',
    enum: ['text', 'image', 'document'],
    default: 'text',
  })
  type?: 'text' | 'image' | 'document' = 'text';
}

export class ChatHistoryDto {
  @ApiProperty({
    description: 'ID do chat (número de telefone)',
    example: '5511999999999',
  })
  chatId: string;

  @ApiPropertyOptional({
    description: 'Limite de mensagens a retornar',
    default: 50,
    minimum: 1,
    maximum: 1000,
  })
  limit?: number = 50;
}

export class MessageResponseDto {
  @ApiProperty({ description: 'Indica se a operação foi bem-sucedida' })
  success: boolean;

  @ApiPropertyOptional({ description: 'ID da mensagem enviada' })
  messageId?: string;

  @ApiPropertyOptional({ description: 'Destinatário da mensagem' })
  to?: string;

  @ApiPropertyOptional({ description: 'Conteúdo da mensagem' })
  message?: string;

  @ApiPropertyOptional({ description: 'Mensagem de erro, se houver' })
  error?: string;
}

export class ConnectionStatusDto {
  @ApiProperty({ description: 'Status da conexão com WhatsApp' })
  connected: boolean;

  @ApiPropertyOptional({
    description: 'QR Code para autenticação (quando não conectado)',
  })
  qrCode?: string;

  @ApiProperty({ description: 'Estado atual da conexão' })
  state: string;
}
