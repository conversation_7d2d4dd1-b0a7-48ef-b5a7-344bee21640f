import {
    Body,
    Controller,
    Get,
    HttpException,
    HttpStatus,
    Param,
    Post,
    Query,
} from '@nestjs/common';
import { SendMessageDto } from './dto/send-message.dto';
import { ChatHistory, WhatsappService } from './whatsapp.service';

@Controller('whatsapp')
export class WhatsappController {
  constructor(private readonly whatsappService: WhatsappService) {}

  @Get('status')
  getConnectionStatus() {
    return this.whatsappService.getConnectionStatus();
  }

  @Post('send-message')
  async sendMessage(@Body() sendMessageDto: SendMessageDto) {
    try {
      const result = await this.whatsappService.sendMessage(sendMessageDto);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('chat/:chatId/history')
  async getChatHistory(
    @Param('chatId') chatId: string,
    @Query('limit') limit?: string,
  ): Promise<{ success: boolean; data: ChatHistory }> {
    try {
      const messageLimit = limit ? parseInt(limit, 10) : 50;
      const history = await this.whatsappService.getChatHistory(chatId, messageLimit);
      
      return {
        success: true,
        data: history,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('chats')
  async getAllChats(): Promise<{ success: boolean; data: ChatHistory[] }> {
    try {
      const chats = await this.whatsappService.getAllChats();
      
      return {
        success: true,
        data: chats,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('disconnect')
  async disconnect() {
    try {
      await this.whatsappService.disconnect();
      return {
        success: true,
        message: 'WhatsApp disconnected successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
