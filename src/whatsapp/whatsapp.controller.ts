import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  AllChatsResponse,
  ChatHistoryResponse,
  DisconnectResponse,
  ErrorResponse,
  SendMessageResponse
} from './dto/response.dto';
import { ConnectionStatusDto, SendMessageDto } from './dto/send-message.dto';
import { ChatHistory, WhatsappService } from './whatsapp.service';

@ApiTags('whatsapp')
@Controller('whatsapp')
export class WhatsappController {
  constructor(private readonly whatsappService: WhatsappService) {}

  @Get('status')
  @ApiOperation({
    summary: 'Verificar status da conexão',
    description: 'Retorna o status atual da conexão com WhatsApp e QR Code se necessário'
  })
  @ApiResponse({
    status: 200,
    description: 'Status da conexão retornado com sucesso',
    type: ConnectionStatusDto
  })
  getConnectionStatus(): ConnectionStatusDto {
    return this.whatsappService.getConnectionStatus();
  }

  @Post('send-message')
  @ApiOperation({
    summary: 'Enviar mensagem',
    description: 'Envia uma mensagem de texto para um número de WhatsApp'
  })
  @ApiBody({
    type: SendMessageDto,
    description: 'Dados da mensagem a ser enviada'
  })
  @ApiResponse({
    status: 200,
    description: 'Mensagem enviada com sucesso',
    type: SendMessageResponse
  })
  @ApiResponse({
    status: 400,
    description: 'Erro ao enviar mensagem',
    type: ErrorResponse
  })
  async sendMessage(@Body() sendMessageDto: SendMessageDto) {
    try {
      const result = await this.whatsappService.sendMessage(sendMessageDto);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('chat/:chatId/history')
  @ApiOperation({
    summary: 'Buscar histórico de conversa',
    description: 'Retorna o histórico de mensagens de uma conversa específica'
  })
  @ApiParam({
    name: 'chatId',
    description: 'ID do chat (número de telefone)',
    example: '5511999999999'
  })
  @ApiQuery({
    name: 'limit',
    description: 'Limite de mensagens a retornar',
    required: false,
    example: 50
  })
  @ApiResponse({
    status: 200,
    description: 'Histórico retornado com sucesso',
    type: ChatHistoryResponse
  })
  @ApiResponse({
    status: 400,
    description: 'Erro ao buscar histórico',
    type: ErrorResponse
  })
  async getChatHistory(
    @Param('chatId') chatId: string,
    @Query('limit') limit?: string,
  ): Promise<{ success: boolean; data: ChatHistory }> {
    try {
      const messageLimit = limit ? parseInt(limit, 10) : 50;
      const history = await this.whatsappService.getChatHistory(chatId, messageLimit);

      return {
        success: true,
        data: history,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('chats')
  @ApiOperation({
    summary: 'Listar todas as conversas',
    description: 'Retorna uma lista de todas as conversas ativas'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de conversas retornada com sucesso',
    type: AllChatsResponse
  })
  @ApiResponse({
    status: 400,
    description: 'Erro ao listar conversas',
    type: ErrorResponse
  })
  async getAllChats(): Promise<{ success: boolean; data: ChatHistory[] }> {
    try {
      const chats = await this.whatsappService.getAllChats();

      return {
        success: true,
        data: chats,
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('disconnect')
  @ApiOperation({
    summary: 'Desconectar do WhatsApp',
    description: 'Desconecta a sessão atual do WhatsApp'
  })
  @ApiResponse({
    status: 200,
    description: 'Desconectado com sucesso',
    type: DisconnectResponse
  })
  @ApiResponse({
    status: 400,
    description: 'Erro ao desconectar',
    type: ErrorResponse
  })
  async disconnect() {
    try {
      await this.whatsappService.disconnect();
      return {
        success: true,
        message: 'WhatsApp disconnected successfully',
      };
    } catch (error: any) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
