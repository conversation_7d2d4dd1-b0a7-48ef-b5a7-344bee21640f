# WhatsApp Integration Service

Uma API REST construída com NestJS para integração com WhatsApp usando a biblioteca [Baileys](https://baileys.wiki/docs/intro/).

## 🚀 Funcionalidades

- ✅ Envio de mensagens de texto
- ✅ Visualização do histórico de conversas
- ✅ Listagem de todas as conversas
- ✅ Status de conexão em tempo real
- ✅ Armazenamento de mensagens em memória
- ✅ Autenticação via QR Code

## 🛠️ Tecnologias

- **NestJS** - Framework Node.js
- **Baileys** - Biblioteca para WhatsApp Web API
- **TypeScript** - Linguagem de programação
- **Axios** - Cliente HTTP para testes

## 📋 Pré-requisitos

- Node.js (versão 16 ou superior)
- npm ou yarn
- WhatsApp instalado no celular para escaneamento do QR Code

## 🔧 Instalação

1. **Clone o repositório:**
```bash
git clone <repository-url>
cd whatsapp-integration-service
```

2. **Instale as dependências:**
```bash
npm install
```

3. **Inicie o servidor em modo de desenvolvimento:**
```bash
npm run start:dev
```

4. **Conecte ao WhatsApp:**
   - Ao iniciar pela primeira vez, um QR Code será exibido no terminal
   - Abra o WhatsApp no seu celular
   - Vá em **Configurações > Dispositivos conectados > Conectar um dispositivo**
   - Escaneie o QR Code exibido no terminal
   - Aguarde a confirmação da conexão

## 🚀 Como usar

### Verificar status da conexão
```bash
curl http://localhost:3000/whatsapp/status
```

### Enviar mensagem
```bash
curl -X POST http://localhost:3000/whatsapp/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "to": "5511999999999",
    "message": "Olá! Esta é uma mensagem de teste.",
    "type": "text"
  }'
```

### Buscar histórico de conversa
```bash
curl http://localhost:3000/whatsapp/chat/5511999999999/history?limit=20
```

## 📚 Documentação da API

Para documentação completa da API, consulte o arquivo [WHATSAPP_API_GUIDE.md](./WHATSAPP_API_GUIDE.md).

### Endpoints principais:

- `GET /whatsapp/status` - Verificar status da conexão
- `POST /whatsapp/send-message` - Enviar mensagem
- `GET /whatsapp/chat/{chatId}/history` - Buscar histórico
- `GET /whatsapp/chats` - Listar conversas
- `POST /whatsapp/disconnect` - Desconectar

## 🧪 Testando a API

### Usando o arquivo de teste HTTP
Use o arquivo `test-api.http` com a extensão REST Client do VS Code.

### Usando o script de exemplo
```bash
node example-usage.js
```

### Usando curl
Veja exemplos de curl no arquivo [WHATSAPP_API_GUIDE.md](./WHATSAPP_API_GUIDE.md).

## 📁 Estrutura do Projeto

```
src/
├── whatsapp/
│   ├── dto/
│   │   └── send-message.dto.ts
│   ├── message-store.service.ts
│   ├── whatsapp.controller.ts
│   ├── whatsapp.service.ts
│   └── whatsapp.module.ts
├── app.module.ts
└── main.ts
```

## ⚠️ Considerações Importantes

1. **Credenciais**: As credenciais são salvas em `auth_info_baileys/`
2. **Números**: Use formato internacional (ex: 5511999999999)
3. **Rate Limiting**: Implemente controle de taxa para evitar spam
4. **Produção**: Configure adequadamente para ambiente de produção

## 🔧 Scripts Disponíveis

```bash
# Desenvolvimento
npm run start:dev

# Produção
npm run build
npm run start:prod

# Testes
npm run test
npm run test:e2e
```

## 📄 Licença

Este projeto está sob a licença MIT.
