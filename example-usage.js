/**
 * Exemplo de uso da API WhatsApp
 * Execute este script após iniciar o servidor com: npm run start:dev
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/whatsapp';

// Função para aguardar um tempo
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

async function testWhatsAppAPI() {
  try {
    console.log('🚀 Testando API WhatsApp...\n');

    // 1. Verificar status da conexão
    console.log('1. Verificando status da conexão...');
    const statusResponse = await axios.get(`${API_BASE_URL}/status`);
    console.log('Status:', statusResponse.data);
    
    if (!statusResponse.data.connected) {
      console.log('⚠️  WhatsApp não está conectado. Verifique o QR Code no terminal do servidor.');
      if (statusResponse.data.qrCode) {
        console.log('QR Code disponível para escaneamento.');
      }
      return;
    }

    console.log('✅ WhatsApp conectado!\n');

    // 2. Enviar mensagem de teste
    console.log('2. Enviando mensagem de teste...');
    const phoneNumber = '5511999999999'; // Substitua pelo número real
    
    try {
      const sendResponse = await axios.post(`${API_BASE_URL}/send-message`, {
        to: phoneNumber,
        message: `Olá! Esta é uma mensagem de teste enviada em ${new Date().toLocaleString()}`,
        type: 'text'
      });
      
      console.log('Mensagem enviada:', sendResponse.data);
    } catch (error) {
      console.log('❌ Erro ao enviar mensagem:', error.response?.data || error.message);
    }

    // Aguardar um pouco antes de buscar o histórico
    await sleep(2000);

    // 3. Buscar histórico da conversa
    console.log('\n3. Buscando histórico da conversa...');
    try {
      const historyResponse = await axios.get(`${API_BASE_URL}/chat/${phoneNumber}/history?limit=10`);
      console.log('Histórico da conversa:', JSON.stringify(historyResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Erro ao buscar histórico:', error.response?.data || error.message);
    }

    // 4. Listar todas as conversas
    console.log('\n4. Listando todas as conversas...');
    try {
      const chatsResponse = await axios.get(`${API_BASE_URL}/chats`);
      console.log('Conversas encontradas:', chatsResponse.data.data.length);
      console.log('Conversas:', JSON.stringify(chatsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Erro ao listar conversas:', error.response?.data || error.message);
    }

    console.log('\n✅ Teste concluído!');

  } catch (error) {
    console.error('❌ Erro geral:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Certifique-se de que o servidor está rodando com: npm run start:dev');
    }
  }
}

// Executar o teste
testWhatsAppAPI();

// Exemplo de monitoramento contínuo do status
async function monitorConnection() {
  console.log('\n🔄 Iniciando monitoramento da conexão...');
  
  setInterval(async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/status`);
      const status = response.data;
      
      console.log(`[${new Date().toLocaleTimeString()}] Status: ${status.connected ? '🟢 Conectado' : '🔴 Desconectado'} | Estado: ${status.state}`);
      
      if (status.qrCode && !status.connected) {
        console.log('📱 QR Code disponível para escaneamento');
      }
    } catch (error) {
      console.log(`[${new Date().toLocaleTimeString()}] ❌ Erro ao verificar status:`, error.message);
    }
  }, 10000); // Verificar a cada 10 segundos
}

// Descomente a linha abaixo para ativar o monitoramento contínuo
// monitorConnection();
