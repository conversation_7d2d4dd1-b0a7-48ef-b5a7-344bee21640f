{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "KFKZ60tmaBMxFW8TK4ejK8FH4TxlEdEnrb/FWbkXfXY="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "igBlNyNpGWMfuV4wzW6N3nmcnYKTFUgk/DwhqijzIgs="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "CCPE2TAOXYvcPwWppELVum/4p07zTCGUYBAbQdvl20w="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "KH401g/0W9+oAlLMgAXSRFl0IOFbD8apQ6SnUJcMF3s="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "QPpKoMJGSUbdzzFsHJ6kMC3fDvHkSGI0Q7A9TYDq4Xg="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "84+GUZE/vTlD2ZxpSa4w3FALXoJxJqto/ldSR2STg1c="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "qFZdYEAGJZ3xYcVt/RAJ6QrsUeUuydn9dAZYvi2zRV0="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "/jtEJYIsW7j3XSBp/7C56z0frbznisW679KQTIEOKBs="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "tXqzULl8emQYXQt9qqrkvMS8bjEO/QzrRZrsaj1674+CJewNyq1mDhBRdgNNLseAB93ZS64J+tfYXPXurVKnDQ=="}, "keyId": 1}, "registrationId": 218, "advSecretKey": "jOe8XsamL9T9kfkggy5QJSHXAXXncwmcPmmPJJ6zQZM=", "processedHistoryMessages": [], "nextPreKeyId": 47, "firstUnuploadedPreKeyId": 47, "accountSyncCounter": 0, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CMKk4zEQr8XhxQYYBSAAKAA=", "accountSignatureKey": "LnZqcZ630cTIcS9LpVLQcqLHL+o0PsiTKO0FUv6P7j8=", "accountSignature": "SP7COR+pZ+zu1X8sVeAC8IWigPLjgmrHoBlzO0YV4IuBGdLtfxjp+jZLcPBcA8Ds2PKxqrEgmCKREG7Exza2CQ==", "deviceSignature": "RUwju0cK+ngSEzXGL7+U4N63Rj6BOgpkjCyGP+QEGKLo3ZK8/Sy4Iht6HMbaXv0FcjVpNulVRYp6S7/Xd8juBw=="}, "me": {"id": "************:<EMAIL>", "lid": "***************:77@lid", "name": "a<PERSON><PERSON>"}, "signalIdentities": [{"identifier": {"name": "************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BS52anGet9HEyHEvS6VS0HKixy/qND7IkyjtBVL+j+4/"}}], "platform": "iphone", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CBIIBQ=="}, "lastAccountSyncTimestamp": **********, "myAppStateKeyId": "AEwAAOL1", "lastPropHash": "1K4hH4"}