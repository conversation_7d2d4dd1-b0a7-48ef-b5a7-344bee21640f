# WhatsApp Integration API

Esta API permite enviar mensagens e visualizar o histórico de conversas do WhatsApp usando a biblioteca Baileys.

## Configuração Inicial

1. **Instalar dependências:**
```bash
npm install
```

2. **Iniciar o servidor:**
```bash
npm run start:dev
```

3. **Conectar ao WhatsApp:**
   - Ao iniciar o servidor pela primeira vez, um QR Code será exibido no terminal
   - Escaneie o QR Code com seu WhatsApp para conectar
   - As credenciais serão salvas na pasta `auth_info_baileys/`

## Endpoints da API

### 1. Verificar Status da Conexão
```http
GET /whatsapp/status
```

**Resposta:**
```json
{
  "connected": true,
  "qrCode": null,
  "state": "open"
}
```

### 2. Enviar Mensagem
```http
POST /whatsapp/send-message
Content-Type: application/json

{
  "to": "5511999999999",
  "message": "Olá! Esta é uma mensagem de teste.",
  "type": "text"
}
```

**Resposta:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "messageId": "3EB0C431C26A1916E9E5",
    "to": "<EMAIL>",
    "message": "Olá! Esta é uma mensagem de teste."
  }
}
```

### 3. Obter Histórico de Conversa
```http
GET /whatsapp/chat/{chatId}/history?limit=50
```

**Exemplo:**
```http
GET /whatsapp/chat/5511999999999/history?limit=20
```

**Resposta:**
```json
{
  "success": true,
  "data": {
    "id": "<EMAIL>",
    "name": "5511999999999",
    "messages": [
      {
        "id": "3EB0C431C26A1916E9E5",
        "from": "<EMAIL>",
        "to": "<EMAIL>",
        "message": "Olá! Como você está?",
        "timestamp": 1672531200,
        "fromMe": false,
        "messageType": "conversation"
      }
    ]
  }
}
```

### 4. Listar Todas as Conversas
```http
GET /whatsapp/chats
```

**Resposta:**
```json
{
  "success": true,
  "data": []
}
```

### 5. Desconectar do WhatsApp
```http
POST /whatsapp/disconnect
```

**Resposta:**
```json
{
  "success": true,
  "message": "WhatsApp disconnected successfully"
}
```

## Formato de Números de Telefone

Os números devem ser enviados no formato internacional sem símbolos:
- ✅ Correto: `5511999999999` (Brasil + DDD + número)
- ❌ Incorreto: `+55 (11) 99999-9999`
- ❌ Incorreto: `11999999999` (sem código do país)

## Tipos de Mensagem Suportados

Atualmente, a API suporta:
- `text`: Mensagens de texto simples

## Estados de Conexão

- `connecting`: Conectando ao WhatsApp
- `open`: Conectado e pronto para uso
- `close`: Desconectado

## Tratamento de Erros

Todos os endpoints retornam erros no formato:
```json
{
  "success": false,
  "message": "Descrição do erro"
}
```

## Logs

Os logs da aplicação incluem:
- Status de conexão
- Mensagens enviadas e recebidas
- Erros de conexão
- QR Code para autenticação

## Estrutura de Arquivos

```
src/
├── whatsapp/
│   ├── dto/
│   │   └── send-message.dto.ts
│   ├── whatsapp.controller.ts
│   ├── whatsapp.service.ts
│   └── whatsapp.module.ts
└── app.module.ts
```

## Considerações de Segurança

1. **Credenciais**: As credenciais do WhatsApp são salvas localmente em `auth_info_baileys/`
2. **Rate Limiting**: Implemente rate limiting para evitar spam
3. **Validação**: Sempre valide os números de telefone antes de enviar mensagens
4. **Logs**: Monitore os logs para detectar atividades suspeitas

## Próximos Passos

Para expandir a funcionalidade, considere implementar:
- Envio de imagens e documentos
- Webhooks para mensagens recebidas
- Grupos do WhatsApp
- Status de entrega das mensagens
- Rate limiting
- Autenticação da API
