### WhatsApp API Test File
### Use this file with REST Client extension in VS Code or similar tools

### 1. Check connection status
GET http://localhost:3000/whatsapp/status
Content-Type: application/json

###

### 2. Send a text message
POST http://localhost:3000/whatsapp/send-message
Content-Type: application/json

{
  "to": "5511999999999",
  "message": "Ol<PERSON>! Esta é uma mensagem de teste da API.",
  "type": "text"
}

###

### 3. Get chat history
GET http://localhost:3000/whatsapp/chat/5511999999999/history?limit=20
Content-Type: application/json

###

### 4. Get all chats
GET http://localhost:3000/whatsapp/chats
Content-Type: application/json

###

### 5. Disconnect from WhatsApp
POST http://localhost:3000/whatsapp/disconnect
Content-Type: application/json

###
